

// pages/supply/supply.js
const searchUtils = require('../../utils/searchUtils');
Page({
  /**
   * 页面的初始数据
   * 页面需要自动加载
   */
  data: {
    supplyList: [], // 供应列表
    loading: false, // 加载状态
    hasMore: true, // 是否有更多数据
    page: 1, // 当前页码
    pageSize: 10, // 每页数据量
    tabActive: 0, // 当前激活的标签页：0-全部
    tabs: ['全部', '乔木', '灌木', '藤本类', '草皮类', '花草', '种子'], // 标签数组
    scrollTop: 0, // 页面滚动位置
    searchValue: '', // 搜索关键词
    plantSuggestions: [], // 植物名称推荐列表
    showSuggestions: false, // 是否显示推荐列表
    searchTimer: null, // 搜索定时器
    sortType: 'latest', // 排序类型：latest-最新发布，nationwide-全国，province-本省，distance5-5公里内，distance50-50公里内，distance500-500公里内
    maxDistance: null, // 最大距离筛选（单位：公里）
    userProvince: null, // 用户所在省份
    showFilter: false, // 是否显示筛选面板
    filters: {
      minPrice: '', // 最低价格
      maxPrice: '', // 最高价格
      exactPrice: '', // 精确价格
      isPriceExact: false, // 是否使用精确价格
      
      category: 'all', // 分类筛选：all-全部，乔木，灌木，藤本类，草皮类，花草，种子
      
      minHeight: '', // 最小高度
      maxHeight: '', // 最大高度
      exactHeight: '', // 精确高度
      isHeightExact: false, // 是否使用精确高度
      
      minCanopy: '', // 最小冠幅
      maxCanopy: '', // 最大冠幅
      exactCanopy: '', // 精确冠幅
      isCanopyExact: false, // 是否使用精确冠幅
      
      minMeterDiameter: '', // 最小米径
      maxMeterDiameter: '', // 最大米径
      exactMeterDiameter: '', // 精确米径
      isMeterDiameterExact: false, // 是否使用精确米径
      
      minCup: '', // 最小杯口
      maxCup: '', // 最大杯口
      exactCup: '', // 精确杯口
      isCupExact: false, // 是否使用精确杯口
      
      minBranchPos: '', // 最小分支点
      maxBranchPos: '', // 最大分支点
      exactBranchPos: '', // 精确分支点
      isBranchPosExact: false, // 是否使用精确分支点
      
      minChestDiameter: '', // 最小胸径
      maxChestDiameter: '', // 最大胸径
      exactChestDiameter: '', // 精确胸径
      isChestDiameterExact: false, // 是否使用精确胸径
      
      minGroundDiameter: '', // 最小地径
      maxGroundDiameter: '', // 最大地径
      exactGroundDiameter: '', // 精确地径
      isGroundDiameterExact: false, // 是否使用精确地径
      
      minClumpCount: '', // 最小丛生数量
      maxClumpCount: '', // 最大丛生数量
      exactClumpCount: '', // 精确丛生数量
      isClumpCountExact: false, // 是否使用精确丛生数量
      
      minClumpDiameter: '', // 最小杆径
      maxClumpDiameter: '', // 最大杆径
      exactClumpDiameter: '', // 精确杆径
      isClumpDiameterExact: false, // 是否使用精确杆径
      
      quality: 'all', // 质量：all-全部，精品，一般，处理
      exactTreeDiameter: '', // 精确树径
      isTreeDiameterExact: false, // 是否使用精确树径
      minTreeDiameter: '', // 最小树径
      maxTreeDiameter: '' // 最大树径
    },
    activeFilters: {}, // 当前激活的筛选条件
    // 预定义数据集合
    categories: ['乔木/灌木', '杯苗', '藤本类', '地被类', '其他'],
    userLocation: null, // 用户当前位置
    userId: null, // 当前用户ID
    currentLocation: null, // 当前页面的位置信息，用于计算距离
    pageScrollY: 0, // Added for page scroll management
    lastRefreshTime: 0, // 上次刷新时间戳
    lastFilterTime: 0, // 上次筛选时间戳
    // 筛选项的显示状态
    visibleSections: {
      price: false, // 默认隐藏价格筛选，与其他筛选项保持一致
      height: false,
      canopy: false,
      treeDiameter: false, // 新增树经区间
      cup: false,
      branchPos: false,
      clumpCount: false, // 丛生数量
      clumpDiameter: false, // 杆径
      quality: true, // 默认展开质量筛选
      category: false // 新增分类筛选
    },
    fromDetailPage: false, // 是否从详情页返回
    needRefresh: undefined, // Added for page refresh management
    navBarHeight: 48, // Added for navigation bar height
    statusBarHeight: 20, // Added for status bar height
    topPadding: 88, // Added for top padding
    topContainerHeight: 140, // Added for top container height
    contentPadding: 228, // Added for content padding
    deviceInfo: {
      width: 0,
      height: 0,
      ratio: 1,
      platform: 'unknown'
    }
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function () {
    // 初始化防抖函数
    this.debounceApplyFilters = this.debounce(() => {
      this.resetList();
      this.applyQuickFilters();
    }, 800); // 800ms延迟，给用户足够的输入时间
    
    // 获取设备信息，计算导航栏高度
    this.getNavBarHeight();
    
    // 新的加载策略：先快速渲染，后台获取位置
    // 1. 立即加载基础数据（无距离信息）
    this.loadSupplyListWithoutLocation();

    // 2. 后台异步获取位置信息，不阻塞页面渲染
    this.initLocationAsync();
    
    // 处理滚动条问题
    this.handleScrollbarIssue();
    
    // 监听窗口尺寸变化
    wx.onWindowResize(this.handleWindowResize);
  },

  /**
   * 加载供应列表（无位置信息版本）- 用于快速首屏渲染
   */
  loadSupplyListWithoutLocation() {
    this.setData({ loading: true });

    const params = {
      page: 1,
      pageSize: this.data.pageSize,
      tabActive: this.data.tabActive,
      tabs: this.data.tabs,
      searchValue: this.data.searchValue,
      userLocation: null, // 明确设置为null，不计算距离
      sortType: this.data.sortType === 'distance' ? 'default' : this.data.sortType, // 距离排序改为默认排序
      maxDistance: null, // 不进行距离筛选
      userProvince: null, // 暂时不进行省份筛选
      activeFilters: this.data.activeFilters
    };

    // 添加快速筛选条件
    const quickFilters = {};
    if (this.data.filters.exactHeight) quickFilters.exactHeight = this.data.filters.exactHeight;
    if (this.data.filters.exactTreeDiameter) quickFilters.exactTreeDiameter = this.data.filters.exactTreeDiameter;
    if (this.data.filters.exactCanopy) quickFilters.exactCanopy = this.data.filters.exactCanopy;
    if (this.data.filters.exactClumpCount) quickFilters.exactClumpCount = this.data.filters.exactClumpCount;
    if (this.data.filters.exactClumpDiameter) quickFilters.exactClumpDiameter = this.data.filters.exactClumpDiameter;
    if (this.data.filters.exactBranchPos) quickFilters.exactBranchPos = this.data.filters.exactBranchPos;
    params.quickFilters = quickFilters;

    wx.cloud.callFunction({
      name: 'quickstartFunctions',
      config: {
        env: wx.cloud.DYNAMIC_CURRENT_ENV
      },
      data: {
        type: 'getSupplyListWithDistance',
        params: params
      }
    }).then(res => {
      const result = res.result;
      if (result.code === 0) {
        this.setData({
          supplyList: result.data || [],
          totalCount: result.totalCount || 0,
          hasMore: result.hasMore || false,
          loading: false,
          page: 1
        });
        console.log('快速加载完成，数据量:', result.data?.length || 0);

        // 快速加载完成后，重新测量top-container高度
        setTimeout(() => {
          this.getTopContainerRealHeight();
        }, 200);
      } else {
        this.setData({ loading: false });
        wx.showToast({
          title: result.message || '加载失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('快速加载失败:', err);
      this.setData({ loading: false });
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    });
  },

  /**
   * 异步初始化位置信息（不阻塞页面渲染）
   */
  initLocationAsync() {
    // 延迟执行，确保页面已完成初始渲染
    setTimeout(() => {
      this.getUserInfo();
    }, 200); // 延迟200ms，给页面充分的渲染时间
  },

  /**
   * 获取用户信息
   */
  getUserInfo: function() {
    // 无论是否登录，都先获取当前位置信息
    this.getCurrentLocation();
    
    const app = getApp();
    
    // 检查用户是否登录 - 仅用于识别用户，不影响位置获取
    if (app.globalData.isLogined && app.globalData.userId) {
      this.setData({
        userId: app.globalData.userId
      });
      
      // 获取用户省份信息
      this.getUserProvince(app.globalData.userId);
    } else {
      // 尝试从本地存储获取
      const userId = wx.getStorageSync('userId');
      if (userId) {
        this.setData({
          userId: userId
        });
        
        // 获取用户省份信息
        this.getUserProvince(userId);
      } else {
        //console.log('用户未登录，但不影响位置获取和距离计算');
      }
    }
  },
  
  /**
   * 获取用户省份信息
   */
  getUserProvince: function(userId) {
    if (!userId) return;
    
    const app = getApp();
    
    // // 1. 首先检查全局缓存
    // if (app.globalData.userProvince) {
      
    //   this.setData({
    //     userProvince: app.globalData.userProvince
    //   });
    //   return;
    // }
    
    // // 2. 然后检查本地存储
    // const cachedProvince = wx.getStorageSync('userProvince');
    // if (cachedProvince) {
    
    //   this.setData({
    //     userProvince: cachedProvince
    //   });
    //   // 同时更新全局缓存
    //   app.globalData.userProvince = cachedProvince;
    //   wx.setStorageSync('userProvince', cachedProvince);
    // }
    
    // 3. 最后才从数据库获取
 
    wx.cloud.database().collection('users')
      .doc(userId)
      .get()
      .then(res => {
        if (res.data && res.data.province) {
          const province = res.data.province;
        
          
          // 设置到页面数据
          this.setData({
            userProvince: province
          });
          
          // 同时缓存到全局和本地
          app.globalData.userProvince = province;
          wx.setStorageSync('userProvince', province);
        } else {
       
        }
      })
     
  },
  
  /**
   * 获取用户位置信息
   */
  getUserLocation: function() {
    // 这个方法保留用于兼容性，直接调用getCurrentLocation
    this.getCurrentLocation();
  },
  
  /**
   * 获取当前位置信息
   */
  getCurrentLocation() {
    // 检查是否已经有位置信息，如果有则直接使用并加载数据
    if (this.data.currentLocation) {
     
      // 直接使用已有位置信息加载数据
      this.loadSupplyList();
      return;
    }
    
  
    wx.getLocation({
      type: 'gcj02',
      isHighAccuracy: true,
      highAccuracyExpireTime: 50,
      success: (res) => {
        
        
        // 确保位置数据格式正确且完整
        const currentLocation = {
          coordinates: [res.longitude, res.latitude],
          longitude: res.longitude,
          latitude: res.latitude
        };
        
        // 打印完整的位置对象，确保数据正确
       
        
        // 保存位置信息到本地缓存，确保可以在其他页面使用
        wx.setStorageSync('userLocation', {
          coordinates: [res.longitude, res.latitude],
          longitude: res.longitude,
          latitude: res.latitude,
          updateTime: new Date().getTime()
        });
        
        this.setData({
          currentLocation: currentLocation
        });
        
       
        
        // 位置获取成功后，静默更新距离信息
        this.updateDistanceInfo();
      },
      fail: (err) => {
        console.error('位置获取失败:', err);
        
        // 尝试从本地缓存获取位置信息
        const cachedLocation = wx.getStorageSync('userLocation');
        if (cachedLocation && cachedLocation.longitude && cachedLocation.latitude) {
        
          this.setData({
            currentLocation: {
              coordinates: cachedLocation.coordinates || [cachedLocation.longitude, cachedLocation.latitude],
              longitude: cachedLocation.longitude,
              latitude: cachedLocation.latitude
            }
          });
          // 使用缓存位置静默更新距离信息
          this.updateDistanceInfo();
          return;
        }
        
        wx.showToast({
          title: '位置获取失败，无法计算距离',
          icon: 'none'
        });
        
        // 位置获取失败，不需要重新加载数据（已经在onLoad中加载过了）
        console.log('位置获取失败，继续使用已加载的数据');
      }
    });
  },

  /**
   * 静默更新距离信息（位置获取成功后调用）
   */
  updateDistanceInfo() {
    // 如果当前没有数据，直接加载
    if (!this.data.supplyList || this.data.supplyList.length === 0) {
      this.loadSupplyList();
      return;
    }

    // 如果已有数据，静默更新距离信息
    const params = {
      page: 1,
      pageSize: this.data.supplyList.length, // 获取当前已显示的数据量
      tabActive: this.data.tabActive,
      tabs: this.data.tabs,
      searchValue: this.data.searchValue,
      userLocation: this.data.currentLocation,
      sortType: this.data.sortType,
      maxDistance: this.data.maxDistance,
      userProvince: this.data.userProvince,
      activeFilters: this.data.activeFilters
    };

    // 添加快速筛选条件
    const quickFilters = {};
    if (this.data.filters.exactHeight) quickFilters.exactHeight = this.data.filters.exactHeight;
    if (this.data.filters.exactTreeDiameter) quickFilters.exactTreeDiameter = this.data.filters.exactTreeDiameter;
    if (this.data.filters.exactCanopy) quickFilters.exactCanopy = this.data.filters.exactCanopy;
    if (this.data.filters.exactClumpCount) quickFilters.exactClumpCount = this.data.filters.exactClumpCount;
    if (this.data.filters.exactClumpDiameter) quickFilters.exactClumpDiameter = this.data.filters.exactClumpDiameter;
    if (this.data.filters.exactBranchPos) quickFilters.exactBranchPos = this.data.filters.exactBranchPos;
    params.quickFilters = quickFilters;

    // 静默调用云函数更新距离信息
    wx.cloud.callFunction({
      name: 'quickstartFunctions',
      config: {
        env: wx.cloud.DYNAMIC_CURRENT_ENV
      },
      data: {
        type: 'getSupplyListWithDistance',
        params: params
      }
    }).then(res => {
      const result = res.result;
      if (result.code === 0 && result.data && result.data.length > 0) {
        // 静默更新数据，添加距离信息
        this.setData({
          supplyList: result.data,
          totalCount: result.totalCount || this.data.totalCount,
          hasMore: result.hasMore !== undefined ? result.hasMore : this.data.hasMore
        });
        console.log('距离信息更新成功，数据量:', result.data.length);
      }
    }).catch(err => {
      console.log('距离信息更新失败:', err);
      // 失败不影响用户体验，不显示错误提示
    });
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 每次显示页面时更新导航栏高度
    this.getNavBarHeight();
    
    // 获取页面栈，判断页面来源
    const pages = getCurrentPages();
    let needRefresh = true; // 默认需要刷新
    let shouldResetFilters = true; // 默认重置筛选条件
    
  
    
    // 尝试重新获取用户信息，确保省份信息是最新的
    const app = getApp();
    if (app.globalData.isLogined && app.globalData.userId) {
      this.getUserProvince(app.globalData.userId);
    } else {
      const userId = wx.getStorageSync('userId');
      if (userId) {
        this.getUserProvince(userId);
      }
    }
    
    // 检查是否从详情页返回
    if (this.data.fromDetailPage === true) {
    
      needRefresh = false;
      shouldResetFilters = false;
      
      // 重置标志，避免影响下次进入
      this.setData({
        fromDetailPage: false
      });
      
      // 从详情页返回时，直接跳过后续的所有数据加载逻辑
    
      
      // 确保tabbar正确显示
      if (typeof this.getTabBar === 'function') {
        this.getTabBar().setData({
          selected: 1
        });
      }
      
      // 处理滚动条问题
      this.handleScrollbarIssue();
      
      return; // 直接返回，不执行后续逻辑
    }
    
    // 如果页面栈长度大于1，说明有前置页面
    if (pages.length > 1) {
      const prevPage = pages[pages.length - 2];
  
      
      // 如果前一个页面是供应详情页，不刷新数据且保留筛选条件
      if (prevPage.route && (
          prevPage.route.includes('detail') && 
          prevPage.route.includes('supply')
        )) {
      
        needRefresh = false;
        shouldResetFilters = false;
      }
    }
    
    // 如果已经明确设置了needRefresh为false，则保持不刷新
    if (this.data.needRefresh === false) {
     
      needRefresh = false;
      // 重置needRefresh，避免影响下次进入
      this.setData({ needRefresh: undefined });
    }
    // 如果需要重置筛选条件（从非详情页切换过来）
    if (shouldResetFilters) {
     
      this.setData({
        tabActive: 0, // 重置为"全部"标签
        sortType: 'latest', // 重置为"最新发布"排序
        filters: {
          minPrice: '',
          maxPrice: '',
          exactPrice: '',
          isPriceExact: false,
          
          minHeight: '',
          maxHeight: '',
          exactHeight: '',
          isHeightExact: false,
          
          minCanopy: '',
          maxCanopy: '',
          exactCanopy: '',
          isCanopyExact: false,
          
                minTreeDiameter: '',
      maxTreeDiameter: '',
      exactTreeDiameter: '',
      isTreeDiameterExact: false,
          
          minCup: '',
          maxCup: '',
          exactCup: '',
          isCupExact: false,
          
          minBranchPos: '',
          maxBranchPos: '',
          exactBranchPos: '',
          isBranchPosExact: false,
          
          minChestDiameter: '',
          maxChestDiameter: '',
          exactChestDiameter: '',
          isChestDiameterExact: false,
          
          minGroundDiameter: '',
          maxGroundDiameter: '',
          exactGroundDiameter: '',
          isGroundDiameterExact: false,
          
          minClumpCount: '',
          maxClumpCount: '',
          exactClumpCount: '',
          isClumpCountExact: false,
          
          minClumpDiameter: '',
          maxClumpDiameter: '',
          exactClumpDiameter: '',
          isClumpDiameterExact: false,
          
          quality: 'all',
          exactTreeDiameter: '',
          isTreeDiameterExact: false,
          minTreeDiameter: '',
          maxTreeDiameter: ''
        },
        activeFilters: {},
        // 重置可见性设置，除了质量筛选外都收起
        visibleSections: {
          price: false,
          height: false,
          canopy: false,
          treeDiameter: false, // 新增树经区间
          cup: false,
          branchPos: false,
          clumpCount: false, // 丛生数量
          clumpDiameter: false, // 杆径
          quality: true, // 质量筛选始终展开
          category: false // 分类筛选
        }
      });
      needRefresh = true; // 强制刷新数据
    }
    
    // 根据判断结果决定是否刷新数据
    if (needRefresh) {
    
      // 需要刷新，重置列表
      this.resetList();
      
      // 记录刷新时间，避免频繁刷新
      this.setData({ 
        lastRefreshTime: Date.now()
      });
      
      // 需要刷新时，直接加载数据
      this.loadSupplyList();
    } else if (!this.data.supplyList || this.data.supplyList.length === 0) {
      console.log('列表为空，加载数据');
      // 如果列表为空，直接加载数据
      this.loadSupplyList();
    } else {
      console.log('保留现有数据，不刷新');
    }
    
    // 确保tabbar正确显示
    this.updateTabBar();

    // 处理滚动条问题
    this.handleScrollbarIssue();

    // 页面显示时重新测量top-container高度，确保布局正确
    // 延迟执行，确保页面完全显示和数据加载完成
    setTimeout(() => {
      this.getTopContainerRealHeight();
    }, 300);
  },
  /**
   * 更新TabBar选中状态
   */
  updateTabBar: function() {
    // 确保tabbar正确显示
    if (typeof this.getTabBar === 'function') {
      const tabBar = this.getTabBar();
      if (tabBar) {
        tabBar.setData({
          active: 1
        });
      }
    }
    
    // 强制更新tabbar - 添加延时确保DOM已经渲染
    setTimeout(() => {
      if (typeof this.getTabBar === 'function') {
        const tabBar = this.getTabBar();
        if (tabBar) {
          tabBar.setData({
            active: 1
          });
        }
      }
    }, 300);
  },
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {
    // 页面隐藏时，保留数据，不清除
    
    // 页面隐藏时，根据需要处理WebSocket连接
    const app = getApp();
   
  },
  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    // 取消窗口尺寸变化监听
    wx.offWindowResize(this.handleWindowResize);
    
    // 如果有WebSocket连接，断开连接
    if (this.messageWatcher) {
      this.messageWatcher.close();
    
    }
  },
  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    // 检查刷新冷却时间
    if (!this.checkCooldown('refresh')) {
      wx.stopPullDownRefresh(); // 如果在冷却期间，直接停止下拉刷新动画
      return;
    }
    
    this.resetList();
    this.loadSupplyList(true);
  },
  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMoreData();
    }
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function (res) {
    // 分享功能已禁用
    return {
      title: '工程直采，农户直卖',
      path: '/pages/home/<USER>',
      imageUrl: 'cloud://miaomuzhongxin-0giu90bpa4cbeaf5.6d69-miaomuzhongxin-0giu90bpa4cbeaf5-1361723888/static/LOG_withoutBackground.png'
    };
  },
  /**
   * 页面滚动事件
   */
  onPageScroll(e) {
    this.setData({
      scrollTop: e.scrollTop
    });
  },
  /**
   * 回到顶部
   */
  onBackTop() {
    wx.pageScrollTo({
      scrollTop: 0,
      duration: 300
    });
  },
  /**
   * 返回首页
   */
  onBackHome() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  },
  /**
   * 重置列表数据
   */
  resetList() {
    this.setData({
      page: 1,
      hasMore: true,
      supplyList: []
    });
  },
  /**
   * 加载更多数据
   */
  loadMoreData() {
    if (this.data.loading || !this.data.hasMore) return;
    
    // 检查刷新冷却时间
    if (!this.checkCooldown('refresh')) {
      return;
    }
    
    this.setData({ 
      loading: true,
      page: this.data.page + 1
    });
    
    this.loadSupplyList();
  },
  /**
   * 标签切换事件处理
   */
  onTabChange: function(e) {
    const index = parseInt(e.currentTarget.dataset.index);
    if (this.data.tabActive === index) return; // 如果点击当前激活的标签，不做处理
    
    // 检查筛选冷却时间
    if (!this.checkCooldown('filter')) {
      return;
    }
    
    this.setData({
      tabActive: index
    });
    
    // 重置列表并重新加载数据
    this.resetList();
    this.loadSupplyList();
  },
  /**
   * 加载供应列表
   * @param {Boolean} isPullDown 是否是下拉刷新
   */
  loadSupplyList(isPullDown = false) {
    this.setData({ loading: true });
    
    // 准备云函数调用参数 - 简化参数，只传递必要信息
    const params = {
      page: this.data.page,
      pageSize: this.data.pageSize,
      tabActive: this.data.tabActive,
      tabs: this.data.tabs,
      searchValue: this.data.searchValue,
      userLocation: this.data.currentLocation, // 直接传递位置信息
      sortType: this.data.sortType, // 添加排序类型
      maxDistance: this.data.maxDistance, // 添加最大距离限制
      userProvince: this.data.userProvince, // 添加用户省份信息用于本省筛选
      activeFilters: this.data.activeFilters // 添加激活的筛选条件
    };
    
    // 添加快速筛选条件
    const quickFilters = {};
    // 只添加已有值的筛选条件
    if (this.data.filters.exactHeight) quickFilters.exactHeight = this.data.filters.exactHeight;
    if (this.data.filters.exactTreeDiameter) quickFilters.exactTreeDiameter = this.data.filters.exactTreeDiameter;
    if (this.data.filters.exactCanopy) quickFilters.exactCanopy = this.data.filters.exactCanopy;
    if (this.data.filters.exactClumpCount) quickFilters.exactClumpCount = this.data.filters.exactClumpCount;
    if (this.data.filters.exactClumpDiameter) quickFilters.exactClumpDiameter = this.data.filters.exactClumpDiameter;
    if (this.data.filters.exactBranchPos) quickFilters.exactBranchPos = this.data.filters.exactBranchPos;
    
    // 添加到参数中
    params.quickFilters = quickFilters;
    
   
    
    
   
    
    // 调用云函数
    wx.cloud.callFunction({
      name: 'quickstartFunctions',
      config: {
        env: wx.cloud.DYNAMIC_CURRENT_ENV
      },
      data: {
        type: 'getSupplyListWithDistance',
        params: params
      }
    }).then(res => {
      const result = res.result;
      
      if (result.code === 0) {
       
        
        // 检查是否需要重试查询
        if (result.needsRetry && result.existingIds && result.existingIds.length > 0) {
         
          
          // 使用指定ID重新查询
          const retryParams = {
            ...params,
            existingIds: result.existingIds
          };
          
          // 重新调用云函数
          wx.cloud.callFunction({
            name: 'quickstartFunctions',
            config: {
              env: wx.cloud.DYNAMIC_CURRENT_ENV
            },
            data: {
              type: 'getSupplyListWithDistance',
              params: retryParams
            }
          }).then(retryRes => {
            const retryResult = retryRes.result;
            
            if (retryResult.code === 0) {
              console.log('重试查询成功，返回数据条数:', retryResult.data.length);
              
              // 合并数据
              const currentList = this.data.page === 1 ? [] : this.data.supplyList;
              const mergedList = [...currentList, ...retryResult.data];
              
              this.setData({
                supplyList: mergedList,
                loading: false,
                hasMore: retryResult.hasMore
              });

              // 重试查询成功后，重新测量top-container高度
              setTimeout(() => {
                this.getTopContainerRealHeight();
              }, 200);
            } else {
              console.error('重试查询失败:', retryResult.msg);
              this.setData({ loading: false });
              
              wx.showToast({
                title: '加载失败，请重试',
                icon: 'none'
              });
            }
            
            if (isPullDown) {
              wx.stopPullDownRefresh();
            }
          }).catch(err => {
            console.error('重试查询调用云函数失败:', err);
            this.setData({ loading: false });
            
            if (isPullDown) {
              wx.stopPullDownRefresh();
            }
            
            wx.showToast({
              title: '加载失败，请重试',
              icon: 'none'
            });
          });
          
          return; // 提前返回，避免执行后续代码
        }
        
        // 检查返回的数据是否有距离信息
        if (result.data.length > 0) {
          const hasDistance = result.data.some(item => item.calculatedDistance);
         
          
       
        }
        
        // 合并数据
        const currentList = this.data.page === 1 ? [] : this.data.supplyList;
        const mergedList = [...currentList, ...result.data];
        
        this.setData({
          supplyList: mergedList,
          loading: false,
          hasMore: result.hasMore
        });

        // 数据加载完成后，重新测量top-container高度
        // 确保在数据渲染完成后进行测量
        setTimeout(() => {
          this.getTopContainerRealHeight();
        }, 200);
      } else {
      
        this.setData({ loading: false });
        
        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      }
      
      if (isPullDown) {
        wx.stopPullDownRefresh();
      }
    }).catch(err => {
    
      this.setData({ loading: false });
      
      if (isPullDown) {
        wx.stopPullDownRefresh();
      }
      
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    });
  },
  /**
   * 搜索框内容变化事件
   */
  onSearchChange(e) {
    const value = e.detail.value;
    this.setData({
      searchValue: value
    });
    
    // 当输入内容不为空时，进行搜索
    if (value.trim()) {
      // 使用防抖，避免频繁请求
      if (this.data.searchTimer) {
        clearTimeout(this.data.searchTimer);
      }
      
      const timer = setTimeout(() => {
        // 调用searchUtils中的搜索植物函数
        searchUtils.searchPlants(value.trim())
          .then(results => {
            this.setData({
              plantSuggestions: results,
              showSuggestions: results.length > 0
            });
         
          })
          .catch(err => {
            console.error('搜索植物失败:', err);
          });
      }, 500); // 延迟500ms执行搜索
      
      this.setData({
        searchTimer: timer
      });
    } else {
      // 清空输入时，隐藏推荐列表
      this.setData({
        plantSuggestions: [],
        showSuggestions: false
      });
    }
  },
  /**
   * 搜索按钮点击事件
   */
  onSearch() {
    // 检查搜索冷却时间
    if (!this.checkCooldown('refresh')) {
      return;
    }
    
    this.resetList();
    this.loadSupplyList();
  },
  /**
   * 搜索取消按钮点击事件
   */
  onSearchCancel() {
    // 检查搜索冷却时间
    if (!this.checkCooldown('refresh')) {
      return;
    }
    
    this.setData({
      searchValue: ''
    });
    
    this.resetList();
    this.loadSupplyList();
  },
  /**
   * 点击供应项，跳转到详情页
   */
  onSupplyItemTap(e) {
    const id = e.currentTarget.dataset.id;
    const index = e.currentTarget.dataset.index;
    const item = this.data.supplyList[index];
    
    // 获取距离信息，如果有的话
    let distanceParam = '';
    if (item && item.calculatedDistance) {
      distanceParam = `&distance=${encodeURIComponent(item.calculatedDistance)}`;
     
    }
    
    wx.navigateTo({
      url: `/pages/supply/detail/detail?id=${id}${distanceParam}`
    });
  },
  /**
   * 发布供应按钮点击事件
   */
  onPublishTap() {
    wx.navigateTo({
      url: '/pages/supply/publish/publish'
    });
  },
  /**
   * 预览图片
   */
  previewImage(e) {
    const { urls, current } = e.currentTarget.dataset;
    
    wx.previewImage({
      urls: urls,
      current: current
    });
  },
  /**
   * 排序方式改变事件
   */
  onSortChange(e) {
    const type = e.currentTarget.dataset.type;
    
    if (this.data.sortType === type) return; // 如果点击当前排序方式，不做处理
    
    // 检查筛选冷却时间
    if (!this.checkCooldown('filter')) {
      return;
    }
    
    // 处理本省筛选
    if (type === 'province') {
      // 检查用户是否登录
      const app = getApp();
      if ((!app.globalData.isLogined || !app.globalData.userId) && !wx.getStorageSync('userId')) {
        wx.showModal({
          title: '提示',
          content: '使用"本省"筛选功能需要先登录',
          confirmText: '去登录',
          success: (res) => {
            if (res.confirm) {
              // 跳转到用户中心页面，让用户可以点击头像登录
              wx.switchTab({
                url: '/pages/user/user'
              });
              
              // 延迟显示提示信息，指导用户点击头像登录
              setTimeout(() => {
                wx.showToast({
                  title: '请点击头像登录',
                  icon: 'none',
                  duration: 3000
                });
              }, 500);
            }
          }
        });
        return; // 未登录，不继续执行
      }
      
      // 检查是否有省份信息（优先使用页面已有数据）
      if (!this.data.userProvince) {
        // 尝试从全局缓存获取
        if (app.globalData.userProvince) {
        
          this.setData({
            userProvince: app.globalData.userProvince
          });
        } 
        // 尝试从本地存储获取
        else if (wx.getStorageSync('userProvince')) {
          const cachedProvince = wx.getStorageSync('userProvince');
       
          this.setData({
            userProvince: cachedProvince
          });
          // 同时更新全局缓存
          app.globalData.userProvince = cachedProvince;
        }
      }
      
      // 最终检查是否有省份信息
      if (!this.data.userProvince) {
        wx.showToast({
          title: '无法获取您的省份信息，请重新登录',
          icon: 'none',
          duration: 2000
        });
        return; // 无省份信息，不继续执行
      } else {
       
      }
    }
    
    // 处理距离筛选
    if (type === 'distance5' || type === 'distance50' || type === 'distance500') {
      // 检查用户是否有位置信息
      if (!this.data.currentLocation) {
        wx.showModal({
          title: '提示',
          content: '使用距离筛选功能需要获取您的位置权限',
          confirmText: '授权位置',
          success: (res) => {
            if (res.confirm) {
              // 尝试获取位置权限
              wx.getLocation({
                type: 'gcj02',
                success: (res) => {
                  // 位置获取成功，保存位置信息
                  const location = {
                    latitude: res.latitude,
                    longitude: res.longitude,
                    coordinates: [res.longitude, res.latitude]
                  };

                  this.setData({
                    currentLocation: location
                  });

                  // 设置排序类型和距离限制
                  this.setDistanceFilter(type);

                  // 重新加载数据
                  this.resetList();
                  this.loadSupplyList();
                },
                fail: (err) => {
                  console.error('获取位置失败:', err);
                  wx.showToast({
                    title: '无法获取位置信息，请检查位置权限',
                    icon: 'none',
                    duration: 2000
                  });
                }
              });
            }
          }
        });
        return; // 无位置信息，不继续执行
      } else {
        // 有位置信息，设置距离筛选
        this.setDistanceFilter(type);
      }
    } else {
      // 非距离筛选，直接设置排序类型并清除距离限制
      this.setData({
        sortType: type,
        maxDistance: null
      });
    }
    
    // 重置列表并重新加载数据
    this.resetList();
    this.loadSupplyList();
  },
  
  /**
   * 设置距离筛选
   */
  setDistanceFilter(type) {
    let maxDistance = null;
    
    // 根据筛选类型设置最大距离
    switch (type) {
      case 'distance5':
        maxDistance = 5;
        break;
      case 'distance50':
        maxDistance = 50;
        break;
      case 'distance500':
        maxDistance = 500;
        break;
      default:
        maxDistance = null;
    }
    
    // 检查当前位置信息
    if ((type === 'distance5' || type === 'distance50' || type === 'distance500') && !this.data.currentLocation) {
      // 如果选择了距离筛选但没有位置信息，尝试重新获取位置
      wx.showLoading({
        title: '获取位置中...',
        mask: true
      });
      
      this.getCurrentLocation()
        .then(() => {
          if (this.data.currentLocation) {
            // 成功获取位置，设置筛选类型和距离
            this.setData({
              sortType: type,
              maxDistance: maxDistance
            });
            
            // 重置列表并加载新数据
            this.resetList();
            this.loadSupplyList();
          } else {
            // 获取位置失败，提示用户并重置为默认筛选
            wx.showToast({
              title: '无法获取位置，请检查定位权限',
              icon: 'none',
              duration: 2000
            });
            
            this.setData({
              sortType: 'latest'
            });
          }
          
          wx.hideLoading();
        })
        .catch(err => {
          console.error('获取位置失败:', err);
          
          wx.hideLoading();
          wx.showToast({
            title: '获取位置失败，请稍后重试',
            icon: 'none',
            duration: 2000
          });
          
          // 重置为默认筛选
          this.setData({
            sortType: 'latest'
          });
        });
    } else {
      // 正常设置筛选类型和距离
      this.setData({
        sortType: type,
        maxDistance: maxDistance
      });
      
      // 重置列表并加载新数据
      this.resetList();
      this.loadSupplyList();
    }
  },
  /**
   * 切换筛选面板
   */
  toggleFilter: function() {
    // 切换筛选面板显示状态
    const showFilter = !this.data.showFilter;
    
    if (showFilter) {
      // 打开筛选面板时，确保质量筛选是展开的
      this.setData({
        showFilter: showFilter,
        'visibleSections.quality': true // 设置质量筛选为展开状态
      });
      
      // 使用setTimeout确保DOM更新后，额外再次确认质量筛选展开
      setTimeout(() => {
        this.setData({
          'visibleSections.quality': true
        });
      }, 100);
    } else {
      // 关闭筛选面板时，只更新面板状态
      this.setData({
        showFilter: showFilter
      });
    }
    
    // 控制页面滚动
    if (showFilter) {
      this.disablePageScroll();
      
      // 强制将筛选面板置于最顶层
      setTimeout(() => {
        const query = wx.createSelectorQuery();
        query.select('.filter-panel').boundingClientRect(rect => {
          if (rect) {
          }
        }).exec();
      }, 300);
    } else {
      this.enablePageScroll();
    }
  },
  /**
   * 关闭筛选面板
   */
  closeFilter() {
    this.setData({
      showFilter: false
    });
    
    this.enablePageScroll();
  },
  /**
   * 禁用页面滚动
   */
  disablePageScroll() {
    this.setData({
      pageScrollY: this.data.scrollTop
    });
  },
  /**
   * 启用页面滚动
   */
  enablePageScroll() {
    // 恢复页面滚动
  },
  /**
   * 应用筛选条件
   */
  applyFilters() {
    // 检查筛选冷却时间
    if (!this.checkCooldown('filter')) {
      return;
    }
    
    // 确保分类筛选被包含在activeFilters中
    const filters = {...this.data.filters};
    
    this.setData({
      activeFilters: filters,
      showFilter: false
    });
    
    this.enablePageScroll();
    this.resetList();
    this.loadSupplyList();
  },
  /**
   * 重置筛选条件
   */
  resetFilters() {
    // 重置所有筛选条件
    this.setData({
      filters: {
        minPrice: '',
        maxPrice: '',
        exactPrice: '',
        isPriceExact: false,
        
        minHeight: '',
        maxHeight: '',
        exactHeight: '',
        isHeightExact: false,
        
        minCanopy: '',
        maxCanopy: '',
        exactCanopy: '',
        isCanopyExact: false,
        
        minMeterDiameter: '',
        maxMeterDiameter: '',
        exactMeterDiameter: '',
        isMeterDiameterExact: false,
        
        minCup: '',
        maxCup: '',
        exactCup: '',
        isCupExact: false,
        
        minBranchPos: '',
        maxBranchPos: '',
        exactBranchPos: '',
        isBranchPosExact: false,
        
        minChestDiameter: '',
        maxChestDiameter: '',
        exactChestDiameter: '',
        isChestDiameterExact: false,
        
        minGroundDiameter: '',
        maxGroundDiameter: '',
        exactGroundDiameter: '',
        isGroundDiameterExact: false,
        
        minClumpCount: '',
        maxClumpCount: '',
        exactClumpCount: '',
        isClumpCountExact: false,
        
        minClumpDiameter: '',
        maxClumpDiameter: '',
        exactClumpDiameter: '',
        isClumpDiameterExact: false,
        
        quality: 'all',
        category: 'all', // 添加分类重置
        exactTreeDiameter: '',
        isTreeDiameterExact: false,
        minTreeDiameter: '',
        maxTreeDiameter: ''
      },
      activeFilters: {},
      // 重置可见性设置，除了质量筛选外都收起
      visibleSections: {
        price: false,
        height: false,
        canopy: false,
        treeDiameter: false, // 新增树经区间
        cup: false,
        branchPos: false,
        clumpCount: false, // 丛生数量
        clumpDiameter: false, // 杆径
        quality: true, // 质量筛选保持展开
        category: false // 分类筛选
      }
    });
    
    // 提示用户
    wx.showToast({
      title: '筛选条件已重置',
      icon: 'success',
      duration: 1500
    });
  },
  /**
   * 防止滚动穿透
   */
  preventTouchMove() {
    return false;
  },
  /**
   * 确保筛选面板内容可滚动
   */
  onFilterTouchStart(e) {
    // 记录触摸开始位置
    this.startY = e.touches[0].pageY;
  },
  
  onFilterTouchMove(e) {
    // 计算滑动距离
    const moveY = e.touches[0].pageY - this.startY;
    const filterContent = this.selectComponent('.filter-content');
    
    // 允许筛选内容区域滚动
    if (filterContent) {
      return true;
    }
    
    // 防止其他区域滚动穿透
    return false;
  },
  /**
   * 切换价格筛选模式
   */
  togglePriceMode() {
    this.setData({
      'filters.isPriceExact': !this.data.filters.isPriceExact
    });
  },
  /**
   * 切换高度筛选模式
   */
  toggleHeightMode() {
    this.setData({
      'filters.isHeightExact': !this.data.filters.isHeightExact
    });
  },
  /**
   * 切换冠幅筛选模式
   */
  toggleCanopyMode() {
    this.setData({
      'filters.isCanopyExact': !this.data.filters.isCanopyExact
    });
  },
  /**
   * 切换米径筛选模式
   */
  toggleMeterDiameterMode() {
    this.setData({
      'filters.isMeterDiameterExact': !this.data.filters.isMeterDiameterExact
    });
  },
  /**
   * 切换杯口筛选模式
   */
  toggleCupMode() {
    this.setData({
      'filters.isCupExact': !this.data.filters.isCupExact
    });
  },
  /**
   * 精确价格输入事件
   */
  onExactPriceChange(e) {
    this.setData({
      'filters.exactPrice': e.detail.value
    });
  },
  /**
   * 精确高度输入事件
   */
  onExactHeightChange(e) {
    this.setData({
      'filters.exactHeight': e.detail.value
    });
  },
  /**
   * 精确冠幅输入事件
   */
  onExactCanopyChange(e) {
    this.setData({
      'filters.exactCanopy': e.detail.value
    });
  },
  /**
   * 精确米径输入事件
   */
  onExactMeterDiameterChange(e) {
    this.setData({
      'filters.exactMeterDiameter': e.detail.value
    });
  },
  /**
   * 精确杯口输入事件
   */
  onExactCupChange(e) {
    this.setData({
      'filters.exactCup': e.detail.value
    });
  },
  /**
   * 最低价格输入事件
   */
  onMinPriceChange(e) {
    this.setData({
      'filters.minPrice': e.detail.value
    });
  },
  /**
   * 最高价格输入事件
   */
  onMaxPriceChange(e) {
    this.setData({
      'filters.maxPrice': e.detail.value
    });
  },
  /**
   * 最小高度输入事件
   */
  onMinHeightChange(e) {
    this.setData({
      'filters.minHeight': e.detail.value
    });
  },
  /**
   * 最大高度输入事件
   */
  onMaxHeightChange(e) {
    this.setData({
      'filters.maxHeight': e.detail.value
    });
  },
  /**
   * 最小冠幅输入事件
   */
  onMinCanopyChange(e) {
    this.setData({
      'filters.minCanopy': e.detail.value
    });
  },
  /**
   * 最大冠幅输入事件
   */
  onMaxCanopyChange(e) {
    this.setData({
      'filters.maxCanopy': e.detail.value
    });
  },
  /**
   * 最小米径输入事件
   */
  onMinMeterDiameterChange(e) {
    this.setData({
      'filters.minMeterDiameter': e.detail.value
    });
  },
  /**
   * 最大米径输入事件
   */
  onMaxMeterDiameterChange(e) {
    this.setData({
      'filters.maxMeterDiameter': e.detail.value
    });
  },
  /**
   * 最小杯口输入事件
   */
  onMinCupChange(e) {
    this.setData({
      'filters.minCup': e.detail.value
    });
  },
  /**
   * 最大杯口输入事件
   */
  onMaxCupChange(e) {
    this.setData({
      'filters.maxCup': e.detail.value
    });
  },
  /**
   * 分类选择事件
   */
  onCategoryChange(e) {
    this.setData({
      'filters.category': e.currentTarget.dataset.category
    });
  },
  /**
   * 质量选择事件
   */
  onQualityChange(e) {
    this.setData({
      'filters.quality': e.currentTarget.dataset.quality
    });
  },
  /**
   * 阻止触摸移动事件
   */
  preventTouchMove() {
    return false;
  },
  /**
   * 切换分支点筛选模式
   */
  toggleBranchPosMode() {
    this.setData({
      'filters.isBranchPosExact': !this.data.filters.isBranchPosExact
    });
  },
  /**
   * 精确分支点输入事件
   */
  onExactBranchPosChange(e) {
    this.setData({
      'filters.exactBranchPos': e.detail.value
    });
  },
  /**
   * 最小分支点输入事件
   */
  onMinBranchPosChange(e) {
    this.setData({
      'filters.minBranchPos': e.detail.value
    });
  },
  /**
   * 最大分支点输入事件
   */
  onMaxBranchPosChange(e) {
    this.setData({
      'filters.maxBranchPos': e.detail.value
    });
  },
  /**
   * 切换胸径筛选模式
   */
  toggleChestDiameterMode() {
    this.setData({
      'filters.isChestDiameterExact': !this.data.filters.isChestDiameterExact
    });
  },
  /**
   * 精确胸径输入事件
   */
  onExactChestDiameterChange(e) {
    this.setData({
      'filters.exactChestDiameter': e.detail.value
    });
  },
  /**
   * 最小胸径输入事件
   */
  onMinChestDiameterChange(e) {
    this.setData({
      'filters.minChestDiameter': e.detail.value
    });
  },
  /**
   * 最大胸径输入事件
   */
  onMaxChestDiameterChange(e) {
    this.setData({
      'filters.maxChestDiameter': e.detail.value
    });
  },
  /**
   * 切换地径筛选模式
   */
  toggleGroundDiameterMode() {
    this.setData({
      'filters.isGroundDiameterExact': !this.data.filters.isGroundDiameterExact
    });
  },
  /**
   * 精确地径输入事件
   */
  onExactGroundDiameterChange(e) {
    this.setData({
      'filters.exactGroundDiameter': e.detail.value
    });
  },
  /**
   * 最小地径输入事件
   */
  onMinGroundDiameterChange(e) {
    this.setData({
      'filters.minGroundDiameter': e.detail.value
    });
  },
  /**
   * 最大地径输入事件
   */
  onMaxGroundDiameterChange(e) {
    this.setData({
      'filters.maxGroundDiameter': e.detail.value
    });
  },
  /**
   * 检查操作冷却时间
   * @param {String} type - 操作类型: 'refresh' 或 'filter'
   * @returns {Boolean} - 是否允许操作
   */
  checkCooldown(type) {
    const now = Date.now();
    const cooldownMs = 1000; // 冷却时间，从3.5秒改为1秒
    let lastTime = 0;
    
    if (type === 'refresh') {
      lastTime = this.data.lastRefreshTime;
      if (now - lastTime < cooldownMs) {
        wx.showToast({
          title: `请等待${((cooldownMs - (now - lastTime)) / 1000).toFixed(1)}秒后再刷新`,
          icon: 'none'
        });
        return false;
      }
      this.setData({ lastRefreshTime: now });
    } else if (type === 'filter') {
      lastTime = this.data.lastFilterTime;
      if (now - lastTime < cooldownMs) {
        wx.showToast({
          title: `请等待${((cooldownMs - (now - lastTime)) / 1000).toFixed(1)}秒后再筛选`,
          icon: 'none'
        });
        return false;
      }
      this.setData({ lastFilterTime: now });
    }
    
    return true;
  },
  /**
   * 清空搜索框内容，但不触发搜索
   */
  clearSearch(e) {
    // 阻止事件冒泡
    if (e && e.stopPropagation) {
      e.stopPropagation();
    }
    
    this.setData({
      searchValue: '',
      showSuggestions: false,
      plantSuggestions: []
    });
    // 不触发搜索，只清除内容
  },
  /**
   * 清除页面数据
   */
  clearPageData() {
    this.setData({
      supplyList: [],
      loading: false,
      hasMore: true,
      page: 1,
      // 保留用户的筛选设置和搜索条件，只清除数据
      // 保留位置信息，避免重新加载时需要重新获取位置
    });
    
  
  },
  /**
   * 切换筛选项的显示/隐藏状态
   */
  toggleFilterSection: function(e) {
    const section = e.currentTarget.dataset.section;
    const visibleSections = { ...this.data.visibleSections };
    visibleSections[section] = !visibleSections[section];
    
    this.setData({
      visibleSections: visibleSections
    });
  },
  /**
   * 切换杆径模式（区间/精确）
   */
  toggleClumpDiameterMode() {
    this.setData({
      'filters.isClumpDiameterExact': !this.data.filters.isClumpDiameterExact
    });
  },
  
  /**
   * 切换丛生模式（区间/精确）
   */
  toggleClumpCountMode() {
    this.setData({
      'filters.isClumpCountExact': !this.data.filters.isClumpCountExact
    });
  },
  
  /**
   * 处理精确杆径输入变化
   */
  onExactClumpDiameterChange(e) {
    this.setData({
      'filters.exactClumpDiameter': e.detail.value
    });
  },
  
  /**
   * 处理精确丛生数量输入变化
   */
  onExactClumpCountChange(e) {
    this.setData({
      'filters.exactClumpCount': e.detail.value
    });
  },
  
  /**
   * 处理最小杆径输入变化
   */
  onMinClumpDiameterChange(e) {
    this.setData({
      'filters.minClumpDiameter': e.detail.value
    });
  },
  
  /**
   * 处理最大杆径输入变化
   */
  onMaxClumpDiameterChange(e) {
    this.setData({
      'filters.maxClumpDiameter': e.detail.value
    });
  },
  
  /**
   * 处理最小丛生数量输入变化
   */
  onMinClumpCountChange(e) {
    this.setData({
      'filters.minClumpCount': e.detail.value
    });
  },
  
  /**
   * 处理最大丛生数量输入变化
   */
  onMaxClumpCountChange(e) {
    this.setData({
      'filters.maxClumpCount': e.detail.value
    });
  },
  // 添加丛生数量输入框处理方法
  onQuickClumpCountChange: function(e) {
    const value = e.detail.value;
    this.setData({
      'filters.exactClumpCount': value,
      'filters.isClumpCountExact': true
    });
    
    this.debounceApplyFilters();
  },
  // 添加杆径输入框处理方法
  onQuickClumpDiameterChange: function(e) {
    const value = e.detail.value;
    this.setData({
      'filters.exactClumpDiameter': value,
      'filters.isClumpDiameterExact': true
    });
    
    this.debounceApplyFilters();
  },
  /**
   * 防抖函数，用于延迟执行函数
   */
  debounce(func, wait = 500) {
    let timeout;
    return function() {
      const context = this;
      const args = arguments;
      clearTimeout(timeout);
      timeout = setTimeout(() => {
        func.apply(context, args);
      }, wait);
    };
  },
  /**
   * 防抖处理的应用筛选方法
   */
  debounceApplyFilters: null, // 会在onLoad中初始化
  /**
   * 应用快速筛选条件
   */
  applyQuickFilters() {
    // 清除页面滚动位置
    wx.pageScrollTo({
      scrollTop: 0,
      duration: 0
    });
    // 重新加载数据
    this.loadSupplyList();
  },
  /**
   * 处理快速筛选条件变化
   */
  // 高度
  onQuickHeightChange: function(e) {
    const value = e.detail.value;
    this.setData({
      'filters.exactHeight': value,
      'filters.isHeightExact': true
    });
    
    this.debounceApplyFilters();
  },
  // 树经
  onQuickTreeDiameterChange: function(e) {
    const value = e.detail.value;
    this.setData({
      'filters.exactTreeDiameter': value,
      'filters.isTreeDiameterExact': true
    });
    
    this.debounceApplyFilters();
  },
  // 冠幅
  onQuickCanopyChange: function(e) {
    const value = e.detail.value;
    this.setData({
      'filters.exactCanopy': value,
      'filters.isCanopyExact': true
    });
    
    this.debounceApplyFilters();
  },
  // 分支点
  onQuickBranchPosChange: function(e) {
    const value = e.detail.value;
    this.setData({
      'filters.exactBranchPos': value,
      'filters.isBranchPosExact': true
    });
    
    this.debounceApplyFilters();
  },
  // 丛生数量
  onQuickClumpCountChange: function(e) {
    const value = e.detail.value;
    this.setData({
      'filters.exactClumpCount': value,
      'filters.isClumpCountExact': true
    });
    
    this.debounceApplyFilters();
  },
  // 杆径
  onQuickClumpDiameterChange: function(e) {
    const value = e.detail.value;
    this.setData({
      'filters.exactClumpDiameter': value,
      'filters.isClumpDiameterExact': true
    });
    
    this.debounceApplyFilters();
  },
  /**
   * 切换树经模式（区间/精确）
   */
  toggleTreeDiameterMode() {
    this.setData({
      'filters.isTreeDiameterExact': !this.data.filters.isTreeDiameterExact
    });
  },
  
  /**
   * 精确树经输入事件
   */
  onExactTreeDiameterChange(e) {
    this.setData({
      'filters.exactTreeDiameter': e.detail.value
    });
  },
  
  /**
   * 最小树经输入事件
   */
  onMinTreeDiameterChange(e) {
    this.setData({
      'filters.minTreeDiameter': e.detail.value
    });
  },
  
  /**
   * 最大树经输入事件
   */
  onMaxTreeDiameterChange(e) {
    this.setData({
      'filters.maxTreeDiameter': e.detail.value
    });
  },
  /**
   * 搜索框获得焦点事件
   */
  onSearchFocus: function() {
    // 如果搜索框已有内容且有推荐项，则显示推荐列表
    if (this.data.searchValue.trim() && this.data.plantSuggestions.length > 0) {
      this.setData({
        showSuggestions: true
      });
    }
  },
  /**
   * 搜索框失去焦点事件
   */
  onSearchBlur: function() {
    // 不再自动隐藏推荐列表，让用户可以继续浏览推荐项目
    // 推荐列表只有在用户主动关闭或选择项目时才隐藏
  },
  /**
   * 关闭推荐列表
   */
  closeSuggestions(e) {
    // 阻止事件冒泡，避免触发父元素的点击事件
    if (e && typeof e.stopPropagation === 'function') {
      e.stopPropagation();
    }
    
    this.setData({
      showSuggestions: false
    });
  },
  /**
   * 选择推荐的植物名称
   */
  onSelectSuggestion(e) {
    const name = e.currentTarget.dataset.name;
    
    this.setData({
      searchValue: name,
      showSuggestions: false
    });
    
    // 自动触发搜索
    this.onSearch();
  },
  /**
   * 阻止事件冒泡
   */
  stopPropagation(e) {
    searchUtils.stopPropagation(e);
  },
  /**
   * 处理滚动条问题
   */
  handleScrollbarIssue: function() {
    // 使用延时确保DOM已经渲染
    setTimeout(() => {
      try {
        // 创建查询对象
        const query = wx.createSelectorQuery();
        
        // 选择排序栏元素
        query.select('.sort-bar').boundingClientRect(rect => {
          if (rect) {
            // 通过动态设置样式确保滚动条不显示
            wx.createSelectorQuery()
              .select('.sort-bar')
              .fields({ node: true, size: true }, function(res) {
                if (res && res.node) {
                  // 设置scroll-view的scrollLeft为0，确保滚动条初始位置正确
                  res.node.scrollLeft = 0;
                }
              })
              .exec();
          }
        }).exec();
      } catch (error) {
        console.error('处理滚动条问题失败:', error);
      }
    }, 300);
  },
  /**
   * 窗口尺寸变化处理函数
   */
  handleWindowResize: function() {
    // 重新获取导航栏高度
    this.getNavBarHeight();
    // 窗口尺寸变化时也需要重新测量
    this.getTopContainerRealHeight();
  },
  /**
   * 获取导航栏高度
   */
  getNavBarHeight: function() {
    try {
      // 使用新的API替代已弃用的wx.getSystemInfoSync
      const windowInfo = wx.getWindowInfo();
      const systemInfo = wx.getDeviceInfo();
      const appBaseInfo = wx.getAppBaseInfo();

      // 获取状态栏高度
      const statusBarHeight = windowInfo.statusBarHeight || 20;
      // 根据平台设置导航栏高度
      const navBarHeight = (appBaseInfo.platform === 'android' ? 48 : 44) + statusBarHeight;
      // 计算顶部内边距（状态栏+导航栏）
      const topPadding = navBarHeight;
      // 先使用一个临时的contentPadding，后续会通过动态测量更新
      const contentPadding = navBarHeight + 60; // 增加初始值，避免初始加载时的遮挡

      this.setData({
        statusBarHeight,
        navBarHeight,
        topPadding,
        contentPadding
      });

      // 延迟执行动态测量，确保DOM完全渲染
      this.getTopContainerRealHeight();

    } catch (e) {

      // 使用默认值
      this.setData({
        statusBarHeight: 20,
        navBarHeight: 68,
        topPadding: 68,
        contentPadding: 150 // 增加默认值，更保守
      });

      // 即使出错也尝试动态测量
      this.getTopContainerRealHeight();
    }
  },

  /**
   * 动态测量top-container实际高度并更新contentPadding
   * 这是解决特定机型遮挡问题的核心方法
   */
  getTopContainerRealHeight: function() {
    // 使用较长的延时确保DOM完全渲染，包括字体加载等
    setTimeout(() => {
      try {
        const query = wx.createSelectorQuery();

        // 主要测量top-container的高度，nav-placeholder高度可以从topPadding获取
        query.select('.top-container').boundingClientRect();

        query.exec((res) => {
          if (res && res[0] && res[0].height > 0) {
            const topContainerHeight = res[0].height;
            const navPlaceholderHeight = res[1] ? res[1].height : this.data.topPadding;

            // 修正计算逻辑：top-container已经通过top:topPadding定位，所以contentPadding只需要考虑top-container的高度
            // contentPadding = topPadding + top-container高度 + 最小安全边距
            const realContentPadding = Math.ceil(this.data.topPadding + topContainerHeight) + 3; // 减少安全边距到3px

            console.log('精确动态测量结果:', {
              topPadding: this.data.topPadding,
              navPlaceholderHeight: navPlaceholderHeight,
              topContainerHeight: topContainerHeight,
              calculatedPadding: this.data.topPadding + topContainerHeight,
              oldContentPadding: this.data.contentPadding,
              newContentPadding: realContentPadding
            });

            // 只有当新值与当前值差异较大时才更新，避免不必要的重渲染
            if (Math.abs(realContentPadding - this.data.contentPadding) > 3) {
              this.setData({
                contentPadding: realContentPadding
              });

              console.log(`已更新contentPadding: ${this.data.contentPadding}px -> ${realContentPadding}px`);
            } else {
              console.log('contentPadding差异较小，无需更新');
            }
          } else {
            console.warn('无法获取top-container高度信息，使用备用方案');
            this.fallbackContentPadding();
          }
        });

      } catch (error) {
        console.error('动态测量top-container高度失败:', error);
        this.fallbackContentPadding();
      }
    }, 600); // 600ms延时，确保所有内容都已渲染
  },

  /**
   * 备用的contentPadding计算方案
   * 当动态测量失败时使用
   */
  fallbackContentPadding: function() {
    try {
      const windowInfo = wx.getWindowInfo();
      const deviceInfo = wx.getDeviceInfo();
      const statusBarHeight = windowInfo.statusBarHeight || 20;

      // 使用与动态测量一致的计算逻辑：topPadding + 估算的top-container高度
      const currentTopPadding = this.data.topPadding || statusBarHeight + 48;
      let estimatedTopContainerHeight;

      // 根据设备平台估算top-container高度（搜索栏 + 排序栏）
      if (deviceInfo.platform === 'android') {
        // Android设备的top-container高度估算
        if (statusBarHeight >= 30) {
          estimatedTopContainerHeight = 110; // 高状态栏设备，UI元素可能更大
        } else {
          estimatedTopContainerHeight = 100;
        }
      } else if (deviceInfo.platform === 'ios') {
        // iOS设备的top-container高度估算
        if (statusBarHeight >= 44) {
          estimatedTopContainerHeight = 105; // 刘海屏设备
        } else {
          estimatedTopContainerHeight = 95;
        }
      } else {
        // 其他平台
        estimatedTopContainerHeight = 115;
      }

      const fallbackPadding = currentTopPadding + estimatedTopContainerHeight + 5; // 5px安全边距

      this.setData({
        contentPadding: fallbackPadding
      });

      console.log(`使用备用方案设置contentPadding: ${currentTopPadding} + ${estimatedTopContainerHeight} + 5 = ${fallbackPadding}px`);

    } catch (e) {
      // 最后的兜底方案：使用当前topPadding + 保守估算
      const safeTopPadding = this.data.topPadding || 88;
      const safeFallback = safeTopPadding + 120; // 120px是保守的top-container高度估算

      this.setData({
        contentPadding: safeFallback
      });
      console.log(`使用最终兜底方案: ${safeTopPadding} + 120 = ${safeFallback}px`);
    }
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {
    // 页面渲染完成后重新计算导航栏高度
    this.getNavBarHeight();

    // 页面初次渲染完成后，进行动态测量
    // 使用较长延时，确保所有内容都已完全渲染
    setTimeout(() => {
      this.getTopContainerRealHeight();
    }, 800);
  },
}); 